variables:
  PATCH_STAGE: "default"
  RUN_ONLY: "ADB_START ADB_STOP"
  PATCH_VERSION: ""
  ADB_START_STOP_CONFIG: "true"
stages:
  - adb_start
  - adb_stop
 
.exec_template:
  allow_failure: true
  tags:
    - barnyard
  except:
    - merge_requests

.adb_start_template:
  extends: .exec_template
  stage: adb_start

.adb_stop_template:
  extends: .exec_template
  stage: adb_stop
  
###################################################
# ADB Start/Stop Jobs
###################################################

adb_start:
  extends: .adb_start_template
  stage: adb_start
  variables:
    PATCH_STAGE: "adb_start"
    ADB_ACTION: "start"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  when: manual
  timeout: 2h
  only:
    variables:
      - $RUN_ONLY =~ /ADB_START/

adb_stop:
  extends: .adb_stop_template
  stage: adb_stop
  variables:
    PATCH_STAGE: "adb_stop"
    ADB_ACTION: "stop"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  when: manual
  timeout: 2h
  only:
    variables:
      - $RUN_ONLY =~ /ADB_STOP/
