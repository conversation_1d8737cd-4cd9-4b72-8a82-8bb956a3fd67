variables:
  PATCH_STAGE: "default"
  PUBLISH: "false"
  SYNC: "false"
  DR: "false"
  RUN_ONLY: "PRECHECK START_MAINTENANCE DISABLE_DATA_VAULT DB_SCRIPTS AAISERVICES FSS_SERVICES APPSERVICES WTSS_REGISTRY DISARTIFACTS DSUI_HC DSENGINE_HC DFCSCORE_HC ADB_START ADB_STOP"
  PATCH_VERSION: ""
stages:
  - publish
  - scan
  - sync
  - precheck
  - start_maintenance
  - config_setup
  - disable_data_vault
  - db_scripts
  - aaiServices
  - fssServices
  - appServices
  - wtss_registry
  - disArtifacts
  - healthCheck
  - enable_data_vault
  - release_environment
  - contentJob
  - initiateDeployment
  - adb_start
  - adb_stop
 
.exec_template:
  allow_failure: true
  tags:
    - barnyard
  except:
    - merge_requests

.publish_template:
  extends: .exec_template
  stage: publish
  
.scan_template:
  extends: .exec_template
  stage: scan

.sync_template:
  extends: .exec_template
  stage: sync

.precheck_template:
  extends: .exec_template
  stage: precheck
  
.start_maintenance_template:
  extends: .exec_template
  stage: start_maintenance  

.disableDataVault_template:
  extends: .exec_template
  stage: disable_data_vault  

.db_scripts_template:
  extends: .exec_template
  stage: db_scripts  

.aaiservices_template:
  extends: .exec_template
  stage: aaiServices  
  
.fss_services_template:
  extends: .exec_template
  stage: fssServices
  
.appServices_template:
  extends: .exec_template
  stage: appServices   

.wtss_template:
  extends: .exec_template
  stage: wtss_registry

.disArtifacts_template:
  extends: .exec_template
  stage: disArtifacts 

.catalog_hc_template:
  extends: .exec_template
  stage: catalog_hc

.dsui_hc_template:
  extends: .exec_template
  stage: dsui_hc

.dsEngine_hc_template:
  extends: .exec_template
  stage: dsengine_hc

.dfcscore_hc_template:
  extends: .exec_template
  stage: dfcscore_hc

.enableDV_template:
  extends: .exec_template
  stage: enable_data_vault  

.release_environment_template:
  extends: .exec_template
  stage: release_environment 
  
.content_job_template:
  extends: .exec_template
  stage: contentJob
  
.initiate_deployment_template:
  extends: .exec_template
  stage: initiateDeployment

.adb_start_template:
  extends: .exec_template
  stage: adb_start

.adb_stop_template:
  extends: .exec_template
  stage: adb_stop
  
###################################################
# ci/cd jobs
###################################################

publish_artifacts:
  extends: .publish_template
  allow_failure: false
  script:
    - chmod 755 -R ./gitlabci/publish_artifacts.sh
    - ./gitlabci/publish_artifacts.sh
  only:
    variables:
      - $SYNC == "true" 
      - $PUBLISH == "true"
      
scan:
  extends: .scan_template
  stage: scan
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  only:
    variables:
      - $SYNC == "true"
  script:
    - chmod 755 -R ./gitlabci/scan.sh
    - ./gitlabci/scan.sh
    
sync:
  extends: .sync_template
  allow_failure: false
  stage: sync
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  only:
    variables:
      - $SYNC == "true"
  script:
    - chmod 755 -R ./gitlabci/sync.sh
    - ./gitlabci/sync.sh
    
precheck:
  extends: .precheck_template
  stage: precheck
  variables:
    PATCH_STAGE: "precheck"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).([2-9]|[1-9][0-2]).([1-9]|[1-9][0-9])|([2-9][5-9]).([1-9]|[1-9][0-9]).([1-9]|[1-9][0-9])/ && $RUN_ONLY =~ /PRECHECK/
      
start_maintenance:
  extends: .start_maintenance_template
  needs: 
    - job: "precheck"
      optional: true   
  stage: start_maintenance
  variables:
    PATCH_STAGE: "start_maintenance"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][4-9]).([0-9]|[1-9][0-9]).([0-9]|[1-9][0-9])/ && $RUN_ONLY =~ /START_MAINTENANCE/
      
disable_data_vault:
  extends: .disableDataVault_template
  needs: 
    - job: "start_maintenance"
      optional: true
  stage: config_setup
  variables:
    PATCH_STAGE: "disableDataVault"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][4-9]).([0-9]|[1-9][0-9]).([0-9]|[1-9][0-9])/ && $RUN_ONLY =~ /DISABLE_DATA_VAULT/
      
db_scripts:
  extends: .db_scripts_template
  needs:
    - job: "start_maintenance"
      optional: true 
    - job: "disable_data_vault"
      optional: true
  stage: db_scripts
  variables:
    PATCH_STAGE: "dbScripts"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).([2-9]|[1-9][0-2]).([1-9]|[1-9][0-9])|([2-9][5-9]).([1-9]|[1-9][0-9]).([1-9]|[1-9][0-9])/ && $RUN_ONLY =~ /DB_SCRIPTS/

aaiServices:
  extends: .aaiservices_template
  needs: 
    - job: "db_scripts"
      optional: true
    # - job: "wtss_registry"
    #   optional: true
  stage: aaiServices
  variables:
    PATCH_STAGE: "aaiServices"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  timeout: 2h
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $PATCH_VERSION =~ /(25).([2-9]|[1-9][0-2]).([1-9]|[1-9][0-9])|([2-9][5-9]).([1-9]|[1-9][0-9]).([1-9]|[1-9][0-9])/ && $RUN_ONLY =~ /AAISERVICES/ 

fssServices:
  extends: .fss_services_template
  needs: 
    - job: "disable_data_vault"
      optional: true
  stage: fssServices
  variables:
    PATCH_STAGE: "fssServices"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore http://gbu-files.us.oracle.com/software/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][4-9]).([0-9]|[1-9][0-9]).([0-9]|[1-9][0-9])/ && $RUN_ONLY =~ /FSS_SERVICES/

appServices:
  extends: .appServices_template
  stage: appServices
  needs: 
    - job: "aaiServices"
      optional: true
    - job: "fssServices"
      optional: true
  variables:
    PATCH_STAGE: "appServices"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][4-9]).([0-9]|[1-9][0-9]).([0-9]|[1-9][0-9])/ && $RUN_ONLY =~ /APPSERVICES/

wtss_registry:
  extends: .wtss_template
  allow_failure: false
  # stage: config_setup
  needs: 
    - job: "start_maintenance"
      optional: true
    - job: "appServices"
      optional: true 
  variables:
    PATCH_STAGE: "wtss_registry"
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $PATCH_VERSION =~ /(25).([2-9]|[1-9][0-2]).([1-9]|[1-9][0-9])|([2-9][5-9]).([1-9]|[1-9][0-9]).([1-9]|[1-9][0-9])/ && $RUN_ONLY =~ /WTSS_REGISTRY/

disArtifacts:
  extends: .disArtifacts_template
  needs: 
    - job: "db_scripts"
      optional: true
  stage: disArtifacts
  variables:
    PATCH_STAGE: "disArtifacts"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  timeout: 2h
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][4-9]).([0-9]|[1-9][0-9]).([0-9]|[1-9][0-9])/ && $RUN_ONLY =~ /DISARTIFACTS/

catalog_hc:
  extends: .catalog_hc_template
  stage: healthCheck
  needs: 
    - job: "appServices"
      optional: true  
    - job: "wtss_registry"
      optional: true
  variables:
    PATCH_STAGE: "catalog_hc"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][5-9]).([0-9]|[0-9][0-9]).([0-9]|[0-9][0-9])/ && $RUN_ONLY =~ /CATALOG_HC/

dsui_hc:
  extends: .dsui_hc_template
  stage: healthCheck
  needs: 
    - job: "appServices"
      optional: true  
    - job: "wtss_registry"
      optional: true
  variables:
    PATCH_STAGE: "dsui_hc"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][5-9]).([0-9]|[0-9][0-9]).([0-9]|[0-9][0-9])/ && $RUN_ONLY =~ /DSUI_HC/

dsengine_hc:
  extends: .dsEngine_hc_template
  stage: healthCheck
  needs: 
    - job: "appServices"
      optional: true  
    - job: "wtss_registry"
      optional: true
  variables:
    PATCH_STAGE: "dsEngine_hc"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][5-9]).([0-9]|[0-9][0-9]).([0-9]|[0-9][0-9])/ && $RUN_ONLY =~ /DSENGINE_HC/

dfcscore_hc:
  extends: .dfcscore_hc_template
  stage: healthCheck
  needs: 
    - job: "appServices"
      optional: true  
    - job: "wtss_registry"
      optional: true
  variables:
    PATCH_STAGE: "dfcscore_hc"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][5-9]).([0-9]|[0-9][0-9]).([0-9]|[0-9][0-9])/ && $RUN_ONLY =~ /DFCSCORE_HC/
      
enable_data_vault:
  extends: .enableDV_template
  stage: enable_data_vault
  needs: 
    - job: "appServices"
      optional: true
  variables:
    PATCH_STAGE: "enableDataVault"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][5-9]).([0-9]|[0-9][0-9]).([0-9]|[0-9][0-9])/ && $RUN_ONLY =~ /ENABLE_DATA_VAULT/
      
release_environment:
  extends: .release_environment_template
  stage: release_environment
  needs: 
    - job: "enable_data_vault"
      optional: true
  variables:
    PATCH_STAGE: "release_environment"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][5-9]).([0-9]|[0-9][0-9]).([0-9]|[0-9][0-9])/ && $RUN_ONLY =~ /RELEASE_ENVIRONMENT/ 

contentJob:
  extends: .content_job_template
  stage: contentJob
  needs: 
    - job: "release_environment"
      optional: true  
  variables:
    PATCH_STAGE: "contentJob"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  when: manual
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][5-9]).([0-9]|[0-9][0-9]).([0-9]|[0-9][0-9])/ && $RUN_ONLY =~ /CONTENT_JOB/

initiateDeployment:
  extends: .initiate_deployment_template
  stage: initiateDeployment
  needs:
    - job: "contentJob"
      optional: true
  variables:
    PATCH_STAGE: "initiateDeployment"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  when: manual
  timeout: 3h
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).(12).([1-9]|[1-9][0-9])|([2-9][5-9]).([0-9]|[0-9][0-9]).([0-9]|[0-9][0-9])/ && $RUN_ONLY =~ /INITIATE_DEPLOYMENT/

adb_start:
  extends: .adb_start_template
  stage: adb_start
  variables:
    PATCH_STAGE: "adb_start"
    ADB_ACTION: "start"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  when: manual
  timeout: 2h
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).([2-9]|[1-9][0-2]).([1-9]|[1-9][0-9])|([2-9][5-9]).([1-9]|[1-9][0-9]).([1-9]|[1-9][0-9])/ && $RUN_ONLY =~ /ADB_START/

adb_stop:
  extends: .adb_stop_template
  stage: adb_stop
  variables:
    PATCH_STAGE: "adb_stop"
    ADB_ACTION: "stop"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - ./gitlabci/deploy_app.sh
  when: manual
  timeout: 2h
  only:
    variables:
      - $SYNC == "false" && $PUBLISH == "false" && $DR == "false" && $PATCH_VERSION =~ /(25).([2-9]|[1-9][0-2]).([1-9]|[1-9][0-9])|([2-9][5-9]).([1-9]|[1-9][0-9]).([1-9]|[1-9][0-9])/ && $RUN_ONLY =~ /ADB_STOP/
