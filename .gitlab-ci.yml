variables:
  PATCH_STAGE: "default"
  RUN_ONLY: "ADB_START ADB_STOP"
  PATCH_VERSION: ""
  ADB_START_STOP_CONFIG: "true"
  # ADB operation variables - can be overridden by GitLab CI variables
  ADB_ACTION: ""
  SUB_NAMESPACES: ""
  # Additional variables that can be passed from GitLab CI
  TENANT_ID: ""
  OST_ENVIRONMENT: "cndevcorp7-phx"
  MSP_PROJ_NAME: "fsgbu-fsafnd"
stages:
  - adb_start
  - adb_stop
 
.exec_template:
  allow_failure: true
  tags:
    - barnyard
  except:
    - merge_requests

.adb_start_template:
  extends: .exec_template
  stage: adb_start

.adb_stop_template:
  extends: .exec_template
  stage: adb_stop
  
###################################################
# ADB Start/Stop Jobs
###################################################

adb_start:
  extends: .adb_start_template
  stage: adb_start
  variables:
    PATCH_STAGE: "adb_start"
    # Set default action, but allow GitLab CI variables to override
    ADB_ACTION: "${ADB_ACTION:-start}"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - echo "Starting ADB operations with the following configuration:"
    - echo "ADB_ACTION=${ADB_ACTION:-start}"
    - echo "SUB_NAMESPACES=${SUB_NAMESPACES}"
    - echo "ADB_START_STOP_CONFIG=${ADB_START_STOP_CONFIG}"
    - echo "TENANT_ID=${TENANT_ID}"
    - echo "OST_ENVIRONMENT=${OST_ENVIRONMENT}"
    - echo "MSP_PROJ_NAME=${MSP_PROJ_NAME}"
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - export ADB_ACTION="${ADB_ACTION:-start}"
    - ./gitlabci/deploy_app.sh
  when: manual
  timeout: 2h
  only:
    variables:
      - $RUN_ONLY =~ /ADB_START/

adb_stop:
  extends: .adb_stop_template
  stage: adb_stop
  variables:
    PATCH_STAGE: "adb_stop"
    # Set default action, but allow GitLab CI variables to override
    ADB_ACTION: "${ADB_ACTION:-stop}"
  allow_failure: false
  image: phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/deployer:latest
  script:
    - echo "Starting ADB operations with the following configuration:"
    - echo "ADB_ACTION=${ADB_ACTION:-stop}"
    - echo "SUB_NAMESPACES=${SUB_NAMESPACES}"
    - echo "ADB_START_STOP_CONFIG=${ADB_START_STOP_CONFIG}"
    - echo "TENANT_ID=${TENANT_ID}"
    - echo "OST_ENVIRONMENT=${OST_ENVIRONMENT}"
    - echo "MSP_PROJ_NAME=${MSP_PROJ_NAME}"
    - curl --noproxy "*" -o /usr/local/bin/artstore https://artifacthub-phx.oci.oraclecorp.com/artifactory/sdaas-dev-local/artstore/artstore_linux_amd64_ol7
    - chmod +x /usr/local/bin/artstore
    - chmod 755 -R ./gitlabci/deploy_app.sh
    - export ADB_ACTION="${ADB_ACTION:-stop}"
    - ./gitlabci/deploy_app.sh
  when: manual
  timeout: 2h
  only:
    variables:
      - $RUN_ONLY =~ /ADB_STOP/
