# ADB Start/Stop Pipeline

A GitLab CI/CD pipeline for starting and stopping Autonomous Database (ADB) instances across multiple sub-namespaces in Oracle Cloud Infrastructure (OCI).

## Overview

This pipeline provides automated management of ADB lifecycle operations with support for:
- **Parallel execution** across multiple sub-namespaces
- **Start/Stop operations** for ADB instances
- **Kubernetes-based job execution** with proper error handling
- **Manual trigger controls** for safety

## Pipeline Stages

### `adb_start`
- Starts ADB instances in specified sub-namespaces
- Runs in parallel across multiple namespaces
- Manual trigger for safety

### `adb_stop`
- Stops ADB instances in specified sub-namespaces
- Runs in parallel across multiple namespaces
- Manual trigger for safety

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `SUB_NAMESPACES` | Comma-separated list of sub-namespaces | Yes | - |
| `ADB_START_STOP_CONFIG` | Enable/disable ADB operations | No | `true` |
| `ADB_ACTION` | Action to perform (`start` or `stop`) | Auto-set by job | `start`/`stop` |
| `RUN_ONLY` | Pipeline filter | No | `ADB_START ADB_STOP` |
| `TENANT_ID` | Tenant identifier | Yes | - |
| `OST_ENVIRONMENT` | Target environment | No | `cndevcorp7-phx` |
| `MSP_PROJ_NAME` | Project name | No | `fsgbu-fsafnd` |

## Usage

### **Method 1: Using GitLab CI Variables**

1. **Set Variables in GitLab CI/CD:**
   - Navigate to your GitLab project → Settings → CI/CD → Variables
   - Add the following variables:
     ```
     SUB_NAMESPACES = "namespace1,namespace2,namespace3"
     ADB_START_STOP_CONFIG = "true"
     TENANT_ID = "your-tenant-id"
     OST_ENVIRONMENT = "your-environment"
     MSP_PROJ_NAME = "your-project-name"
     ```

2. **Trigger Pipeline:**
   - Navigate to GitLab CI/CD → Pipelines
   - Click "Run Pipeline"
   - Manually trigger `adb_start` or `adb_stop` stages
   - Monitor execution logs for progress

### **Method 2: Using Environment Variables (Local/Script)**

1. **Set Environment Variables:**
   ```bash
   export SUB_NAMESPACES="namespace1,namespace2,namespace3"
   export ADB_START_STOP_CONFIG="true"
   export TENANT_ID="your-tenant-id"
   export OST_ENVIRONMENT="your-environment"
   export MSP_PROJ_NAME="your-project-name"
   ```

2. **Run Pipeline:**
   - The pipeline will automatically pick up these variables
   - Variables passed from GitLab CI take precedence over defaults

### **Method 3: Override Variables at Runtime**

When running the pipeline, you can also override variables:
- In GitLab CI interface, click "Run Pipeline"
- Add/modify variables in the "Variables" section before running
- These runtime variables will override both defaults and project-level variables

## Pipeline Execution

1. **Variable Validation:**
   - Pipeline displays all configuration variables at the start
   - Validates required variables before execution
   - Shows clear error messages for missing variables

2. **Parallel Execution:**
   - The pipeline automatically handles parallel execution across sub-namespaces
   - Each namespace operation runs independently
   - Failed operations in one namespace don't affect others

3. **Manual Controls:**
   - Both `adb_start` and `adb_stop` stages require manual trigger
   - This prevents accidental database operations
   - Each stage can be triggered independently

## File Structure

```
├── .gitlab-ci.yml                           # Main CI/CD pipeline configuration
├── artifacts/
│   ├── common_functions.sh                  # Shared utility functions
│   ├── main_adb.sh                         # Main entry point script
│   ├── parallel_adb_executor.sh            # Parallel execution handler
│   ├── setup_adb.sh                        # Single namespace setup
│   ├── update_adb_status.sh                # ADB operation script
│   └── oci-utilities/
│       ├── configure-adb-job.yaml.tpl      # Kubernetes Job template
│       ├── configure-adb-status.cm.yaml    # ConfigMap with ADB logic
│       └── generate-cm.yaml                # OCI configuration template
└── gitlabci/
    ├── ci_pipeline_functions.sh            # Pipeline functions
    └── deploy_app.sh                        # Deployment script
```

## Technical Details

- **Container Image:** `phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/client-base-image:25.02.01`
- **Execution Environment:** Kubernetes Jobs with OCI CLI
- **Timeout:** 2 hours per stage
- **Error Handling:** Comprehensive logging and failure detection
- **Security:** Uses Kubernetes secrets for OCI authentication

## Prerequisites

- GitLab CI/CD environment with Kubernetes executor
- OCI CLI access and authentication configured
- Proper RBAC permissions for Kubernetes Job creation
- ADB instances must exist in target sub-namespaces

## Monitoring

- Pipeline execution logs available in GitLab CI/CD interface
- Individual namespace operations logged separately
- Failed operations clearly identified with error messages
- Job status tracking through Kubernetes Job resources
