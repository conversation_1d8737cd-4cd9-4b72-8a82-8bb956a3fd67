# ADB Start/Stop Pipeline

A GitLab CI/CD pipeline for starting and stopping Autonomous Database (ADB) instances across multiple sub-namespaces in Oracle Cloud Infrastructure (OCI).

## Overview

This pipeline provides automated management of ADB lifecycle operations with support for:
- **Parallel execution** across multiple sub-namespaces
- **Start/Stop operations** for ADB instances
- **Kubernetes-based job execution** with proper error handling
- **Manual trigger controls** for safety

## Pipeline Stages

### `adb_start`
- Starts ADB instances in specified sub-namespaces
- Runs in parallel across multiple namespaces
- Manual trigger for safety

### `adb_stop`
- Stops ADB instances in specified sub-namespaces
- Runs in parallel across multiple namespaces
- Manual trigger for safety

## Environment Variables

| Variable | Description | Required | Default |
|----------|-------------|----------|---------|
| `SUB_NAMESPACES` | Comma-separated list of sub-namespaces | Yes | - |
| `ADB_START_STOP_CONFIG` | Enable/disable ADB operations | No | `true` |
| `ADB_ACTION` | Action to perform (`start` or `stop`) | Auto-set | - |
| `RUN_ONLY` | Pipeline filter | No | `ADB_START ADB_STOP` |

## Usage

1. **Set Environment Variables:**
   ```bash
   export SUB_NAMESPACES="namespace1,namespace2,namespace3"
   export ADB_START_STOP_CONFIG="true"
   ```

2. **Trigger Pipeline:**
   - Navigate to GitLab CI/CD → Pipelines
   - Manually trigger `adb_start` or `adb_stop` stages
   - Monitor execution logs for progress

3. **Parallel Execution:**
   - The pipeline automatically handles parallel execution across sub-namespaces
   - Each namespace operation runs independently
   - Failed operations in one namespace don't affect others

## File Structure

```
├── .gitlab-ci.yml                           # Main CI/CD pipeline configuration
├── artifacts/
│   ├── common_functions.sh                  # Shared utility functions
│   ├── main_adb.sh                         # Main entry point script
│   ├── parallel_adb_executor.sh            # Parallel execution handler
│   ├── setup_adb.sh                        # Single namespace setup
│   ├── update_adb_status.sh                # ADB operation script
│   └── oci-utilities/
│       ├── configure-adb-job.yaml.tpl      # Kubernetes Job template
│       ├── configure-adb-status.cm.yaml    # ConfigMap with ADB logic
│       └── generate-cm.yaml                # OCI configuration template
└── gitlabci/
    ├── ci_pipeline_functions.sh            # Pipeline functions
    └── deploy_app.sh                        # Deployment script
```

## Technical Details

- **Container Image:** `phx.ocir.io/oraclegbudevcorp/cn-shared/sdaas/client-base-image:25.02.01`
- **Execution Environment:** Kubernetes Jobs with OCI CLI
- **Timeout:** 2 hours per stage
- **Error Handling:** Comprehensive logging and failure detection
- **Security:** Uses Kubernetes secrets for OCI authentication

## Prerequisites

- GitLab CI/CD environment with Kubernetes executor
- OCI CLI access and authentication configured
- Proper RBAC permissions for Kubernetes Job creation
- ADB instances must exist in target sub-namespaces

## Monitoring

- Pipeline execution logs available in GitLab CI/CD interface
- Individual namespace operations logged separately
- Failed operations clearly identified with error messages
- Job status tracking through Kubernetes Job resources
