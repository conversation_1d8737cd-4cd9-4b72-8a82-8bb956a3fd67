#!/bin/bash

echo "Starting ADB Start/Stop operation for multiple SUB_NAMESPACEs"

# Get action from GitLab CI variable or parameter
ACTION=${1:-$ADB_ACTION}

# Validate action parameter
if [ -z "$ACTION" ]; then
    echo "Error: Action not specified"
    echo "Usage: ./main_adb.sh [start|stop]"
    echo "Or set ADB_ACTION environment variable"
    exit 1
fi

if [ "$ACTION" != "start" ] && [ "$ACTION" != "stop" ]; then
    echo "Error: Invalid action: $ACTION"
    echo "Expected: start or stop"
    exit 1
fi

export DV_ACTION="$ACTION"
echo "Action: $DV_ACTION"

# Example usage - set your SUB_NAMESPACEs here
# This should be set by GitLab CI variables in actual usage
if [ -z "$SUB_NAMESPACES" ]; then
    echo "Warning: SUB_NAMESPACES not set!"
fi

echo "SUB_NAMESPACEs to process: $SUB_NAMESPACES"

# Make scripts executable
chmod +x setup_adb.sh
chmod +x parallel_adb_executor.sh

# Execute parallel processing
./parallel_adb_executor.sh

echo "Parallel execution completed"
