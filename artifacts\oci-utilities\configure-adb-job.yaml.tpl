---
apiVersion: batch/v1
kind: Job
metadata:
  name: configure-adb-job
spec:
  backoffLimit: 1
  ttlSecondsAfterFinished: 30000
  template:
    metadata:
      annotations:
        version: v1.0
      labels:
        component: configure-adb-job
    spec:
      containers:
        - image: {{REGISTRY}}/{{PREFIX}}-fsgbu-fsafnd-shared/client-base-image:25.02.01
          name: configure-adb-job
          imagePullPolicy: Always
          env:
            - name: ADB_INSTANCE_OCID
              valueFrom:
                secretKeyRef:
                  name: __DB_SECRET__
                  key: ADB_INSTANCE_OCID 
            - name: ADB_INSTANCE_CID
              valueFrom:
                secretKeyRef:
                  name: __DB_SECRET__
                  key: ADB_INSTANCE_CID 
            - name: OCI_REGION
              value: "__OCI_REGION__"
            - name: WALLET_PATH
              value: "__WALLET_PATH__"
            - name: DB_ALIAS
              valueFrom:
                secretKeyRef:
                  name: __DB_SECRET__
                  key: DB_ALIAS 
            - name: DV_ACTION
              value: "__DV_ACTION__"
            - name: CURRENT_CLUSTER
              value: "__CURRENT_CLUSTER__"
          command:
            - 'sh'
            - '-c'
            - '/home/<USER>/configure-adb-status.sh'
          volumeMounts:
            - name: generate-cm-vol
              mountPath: /home/<USER>/generate.sh
              subPath: generate.sh
            - name: configure-adb-status-vol
              mountPath: /home/<USER>/configure-adb-status.sh
              subPath: configure-adb-status.sh
      restartPolicy: Never
      serviceAccountName: SUB_NAMESPACE-admin
      securityContext:
        runAsUser: 1000
      volumes:
        - name: configure-adb-status-vol
          configMap:
            defaultMode: 0777
            name: configure-adb-status
        - name: generate-cm-vol
          configMap:
            defaultMode: 0777
            name: generate-cm
