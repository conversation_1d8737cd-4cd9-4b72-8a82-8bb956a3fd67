---
apiVersion: v1
kind: ConfigMap
metadata:
  name: configure-adb-status
  annotations:
    version: v1.0
data:
  configure-adb-status.sh: |-
    #!/bin/bash
    set +e
    
    export CMPT_OCID=$ADB_INSTANCE_OCID
    /home/<USER>/generate.sh
    CONFIG_FILE="/home/<USER>/config"

    oci --config-file="${CONFIG_FILE}" db autonomous-database get --autonomous-database-id "${ADB_INSTANCE_OCID}" > response.json
    state=$(jq -r '.data."lifecycle-state"' response.json)

    if [ "${state}" != "${DV_ACTION}" ]; then
        echo "ADB is not in state ${DV_ACTION}, proceeding..."

        echo "DV_ACTION: ${DV_ACTION}"
        oci --config-file="${CONFIG_FILE}" db autonomous-database "${DV_ACTION}" --autonomous-database-id "${ADB_INSTANCE_OCID}" > "${DV_ACTION}.json"
        sleep 10s
        oci --config-file="${CONFIG_FILE}" db autonomous-database get --autonomous-database-id "${ADB_INSTANCE_OCID}" > response.json
        state=$(jq -r '.data."lifecycle-state"' response.json)
    
        while [ "${state}" != "${DV_ACTION}" ]
        do
            sleep 30s
            oci --config-file="${CONFIG_FILE}" db autonomous-database get --autonomous-database-id "${ADB_INSTANCE_OCID}" > response.json
            state=$(jq -r '.data."lifecycle-state"' response.json)
            echo "ADB State after ${DV_ACTION}: ${state}"
        done
    
        echo "ADB successfully ${DV_ACTION}"

    else
        echo "ADB is already in state ${DV_ACTION}"
    fi

    echo "ADB ${DV_ACTION} operation completed"
    exit 0
