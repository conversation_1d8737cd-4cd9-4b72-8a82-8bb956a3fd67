---
apiVersion: batch/v1
kind: Job
metadata:
  name: configure-dv-restart
spec:
  backoffLimit: 1
  ttlSecondsAfterFinished: 30000
  template:
    metadata:
      annotations:
        version: v1.0
      labels:
        component: configure-dv-restart
    spec:
      containers:
        - image: {{REGISTRY}}/{{PREFIX}}-fsgbu-fsafnd-shared/client-base-image:22.07.03
          name: configure-dv-restart
          imagePullPolicy: Always
          env:
            - name: ADB_INSTANCE_OCID
              valueFrom:
                secretKeyRef:
                  name: dbaas-details
                  key: ADB_INSTANCE_OCID 
            - name: ADB_INSTANCE_CID
              valueFrom:
                secretKeyRef:
                  name: dbaas-details
                  key: ADB_INSTANCE_CID 
            - name: OCI_REGION
              value: "__OCI_REGION__"
            - name: WALLET_PATH
              value: "__WALLET_PATH__"
            - name: DB_ALIAS
              valueFrom:
                secretKeyRef:
                  name: dbaas-details
                  key: DB_ALIAS 
            - name: DV_ACTION
              value: "__DV_ACTION__"
            - name: CURRENT_CLUSTER
              value: "__CURRENT_CLUSTER__"
          command:
            - 'sh'
            - '-c'
            - '/home/<USER>/configure-dv-status.sh'
          volumeMounts:
            - name: generate-cm-vol
              mountPath: /home/<USER>/generate.sh
              subPath: generate.sh
            - name: execution-cm-vol
              mountPath: /home/<USER>/configure-dv-status.sh
              subPath: configure-dv-status.sh
            - name: execution-cm-vol
              mountPath: /home/<USER>/configure_dv_wallet.sh
              subPath: configure_dv_wallet.sh
            - mountPath: /home/<USER>/.secrets/ocise001
              name: ocise001-vol
              readOnly: true
            - name: sat-vol
              mountPath: /opt/sat/token
              subPath: sat
      restartPolicy: Never
      serviceAccountName: namespace-admin
      securityContext:
        runAsUser: 1000
      volumes:
        - name: execution-cm-vol
          configMap:
            defaultMode: 0777
            name: configure-dv-status
        - name: generate-cm-vol
          configMap:
            defaultMode: 0777
            name: generate-cm
        - name: ocise001-vol
          secret:
            defaultMode: 292
            secretName: ocise001-cred
        - name: sat-vol
          secret:
            secretName: reserved-service-access-token
