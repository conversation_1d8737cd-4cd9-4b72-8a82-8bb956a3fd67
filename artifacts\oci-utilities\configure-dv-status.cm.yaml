---
apiVersion: v1
kind: ConfigMap
metadata:
  name: configure-dv-status
  annotations:
    version: v1.0
data:
  configure-dv-status.sh: |-
    #!/bin/bash
    set +e
    disableDV(){
    sqlplus -s "/@OFSAADVOWNER" << EOF
    set echo on heading off  feedback off SERVEROUTPUT ON trimout on tab off;
    WHENEVER SQLERROR continue;
    prompt - Current Data Vault Status as below;
    SELECT NAME,STATUS FROM DBA_DV_STATUS WHERE NAME='DV_ENABLE_STATUS';
    EXEC DBMS_CLOUD_MACADM.DISABLE_DATABASE_VAULT;
    prompt - Data Vault Disable initiated;
    exit;
    EOF
    }
    enableDV(){
    sqlplus -s "/@OFSAADVOWNER" << EOF
    set echo on heading off  feedback off SERVEROUTPUT ON trimout on tab off;
    WHENEVER SQLERROR continue;
    prompt - Current DV Status as below;
    SELECT NAME,STATUS FROM DBA_DV_STATUS WHERE NAME='DV_ENABLE_STATUS';
    EXEC DBMS_CLOUD_MACADM.ENABLE_DATABASE_VAULT;
    prompt - DV Enable initiated;
    exit;
    EOF
    }
    checkDVStatus(){
    sqlplus -s "/@OFSAADVOWNER" << EOF
    set echo on heading off  feedback off SERVEROUTPUT ON trimout on tab off;
    WHENEVER SQLERROR continue;
    prompt - DV Status after restart as below;
    SELECT NAME,STATUS FROM DBA_DV_STATUS WHERE NAME='DV_ENABLE_STATUS';
    exit;
    EOF
    }
    export CMPT_OCID=$ADB_INSTANCE_OCID
    /home/<USER>/generate.sh
    /home/<USER>/configure_dv_wallet.sh
    export OCI_CLI_SUPPRESS_FILE_PERMISSIONS_WARNING=True
    export PYTHONWARNINGS="ignore"
    CN_NAMESPACE=$(cat ~/.secrets/ocise001/tenant-name)
    CONFIG_FILE="/home/<USER>/config"
    export TNS_ADMIN=/opt/wallet;
    export TNS_URL=`cat ${TNS_ADMIN}/tnsnames.ora | grep -m 1 . |  cut -f2- -d "=" | xargs|tr -d '\n'|tr -d '\r'`
    ADMIN_USER="OFSAADVOWNER"
    SAT=$(cat /opt/sat/token)
    ADMIN_ALIAS=${DB_ALIAS}
    if [[ "$DV_ACTION" == "disable" ]];then
    disableDV
    else
    enableDV
    fi
    echo  "Data Vault Status will update once restart is complete"    
    #Restart ADB
    oci --config-file=${CONFIG_FILE} db autonomous-database restart --autonomous-database-id ${ADB_INSTANCE_OCID} > restart.json
    sleep 10s
    oci --config-file=${CONFIG_FILE} db autonomous-database get --autonomous-database-id ${ADB_INSTANCE_OCID} > response.json
    state=`cat response.json | jq -r '.data."lifecycle-state"'`
    while [ "${state}" != "AVAILABLE" ]
    do
    sleep 30s;
    oci --config-file=${CONFIG_FILE} db autonomous-database get --autonomous-database-id ${ADB_INSTANCE_OCID} > response.json
    state=`cat response.json | jq -r '.data."lifecycle-state"'`
    echo "ADB State after restart : ${state}"
    done
    #get current status after adb restart
    checkDVStatus
    echo "complete..."
    exit 0
  configure_dv_wallet.sh: |-
    #!/bin/bash
    SAT=$(cat /opt/sat/token)
    wallet_location=/opt/wallet
    curl -s --insecure -X GET -H "Authorization:Bearer ${SAT}"  "${WALLET_PATH}"/wallet-DV | jq -r '.walletdata' | base64 -d --wrap=0 > wallet.zip
    mkdir -p ${wallet_location}
    unzip -q wallet.zip -d ${wallet_location}
    sed -i 's|"?/network/admin.*|/opt/wallet/)))|g' /opt/wallet/sqlnet.ora
    rm -rf wallet.zip