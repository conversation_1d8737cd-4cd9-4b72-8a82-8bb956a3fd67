#!/bin/bash

echo "Starting parallel ADB execution for multiple SUB_NAMESPACEs..."

# Function to execute setup for a single SUB_NAMESPACE
execute_SUB_NAMESPACE() {
    local ns=$1
    echo "Starting execution for SUB_NAMESPACE: $ns"
    
    # Export SUB_NAMESPACE for the current process
    export SUB_NAMESPACE=$ns
    
    # Execute the main setup script
    ./setup_adb.sh "$DV_ACTION" 2>&1 | sed "s/^/[$ns] /"
    
    local exit_code=$?
    if [ $exit_code -eq 0 ]; then
        echo "[$ns] Successfully completed"
    else
        echo "[$ns] Failed with exit code $exit_code"
    fi
    
    return $exit_code
}

# Read SUB_NAMESPACEs from environment variable or parameter
if [ -n "$SUB_NAMESPACES" ]; then
    # Convert comma-separated string to array
    IFS=',' read -ra SUB_NAMESPACE_ARRAY <<< "$SUB_NAMESPACES"
else
    echo "Error: SUB_NAMESPACES environment variable is not set"
    echo "Expected format: SUB_NAMESPACES='SUB_NAMESPACE1,SUB_NAMESPACE2,SUB_NAMESPACE3'"
    exit 1
fi

echo "SUB_NAMESPACEs to process: ${SUB_NAMESPACE_ARRAY[@]}"
echo "Total SUB_NAMESPACEs: ${#SUB_NAMESPACE_ARRAY[@]}"

# Array to store background process PIDs
pids=()
failed_SUB_NAMESPACEs=()

# Start parallel execution for each SUB_NAMESPACE
for ns in "${SUB_NAMESPACE_ARRAY[@]}"; do
    # Trim whitespace
    ns=$(echo "$ns" | xargs)
    
    if [ -n "$ns" ]; then
        echo "Launching parallel execution for SUB_NAMESPACE: $ns"
        execute_SUB_NAMESPACE "$ns" &
        pids+=($!)
    fi
done

echo "All parallel executions started. Waiting for completion..."

# Wait for all background processes and collect results
overall_exit_code=0
for i in "${!pids[@]}"; do
    pid=${pids[$i]}
    ns=${SUB_NAMESPACE_ARRAY[$i]}
    
    wait $pid
    exit_code=$?
    
    if [ $exit_code -ne 0 ]; then
        failed_SUB_NAMESPACEs+=("$ns")
        overall_exit_code=1
    fi
done

# Report results
echo "=================================================="
echo "PARALLEL EXECUTION SUMMARY"
echo "=================================================="
echo "Total SUB_NAMESPACEs processed: ${#SUB_NAMESPACE_ARRAY[@]}"
echo "Successful: $((${#SUB_NAMESPACE_ARRAY[@]} - ${#failed_SUB_NAMESPACEs[@]}))"
echo "Failed: ${#failed_SUB_NAMESPACEs[@]}"

if [ ${#failed_SUB_NAMESPACEs[@]} -gt 0 ]; then
    echo "Failed SUB_NAMESPACEs: ${failed_SUB_NAMESPACEs[@]}"
fi

echo "=================================================="

exit $overall_exit_code
