#!/bin/bash
echo "Executing setup_adb.sh for SUB_NAMESPACE: ${SUB_NAMESPACE}"

DV_ACTION=$1;
echo "DV_ACTION: $DV_ACTION";

# Validate DV_ACTION is set
if [ -z "${DV_ACTION}" ]; then
    echo "Error: DV_ACTION parameter is not set"
    echo "Usage: ./setup_adb.sh [start|stop]"
    exit 1
fi

# Validate DV_ACTION value
if [ "${DV_ACTION}" != "start" ] && [ "${DV_ACTION}" != "stop" ]; then
    echo "Error: Invalid DV_ACTION value: ${DV_ACTION}"
    echo "Expected: start or stop"
    exit 1
fi

# Validate SUB_NAMESPACE is set
if [ -z "${SUB_NAMESPACE}" ]; then
    echo "Error: SUB_NAMESPACE variable is not set"
    exit 1
fi

echo "Processing SUB_NAMESPACE: ${SUB_NAMESPACE} with action: ${DV_ACTION}"

# -----------------------------------------------------------
db_secret="dbaas-details"

# Function to create and wait for Kubernetes job completion
k8s_create_job() {
    local SUB_NAMESPACE=$1
    local component=$2
    local job_file=$3
    local timeout_seconds=${4:-1500}
    
    echo "Creating job in SUB_NAMESPACE: ${SUB_NAMESPACE}, component: ${component}"
    
    # Apply the job
    kubectl -n "${SUB_NAMESPACE}" apply -f "${job_file}"
    if [ $? -ne 0 ]; then
        echo "Failed to create job from ${job_file}"
        return 1
    fi
    
    echo "Waiting for job completion (timeout: ${timeout_seconds}s)..."
    
    # Wait for job completion with timeout
    kubectl -n "${SUB_NAMESPACE}" wait --for=condition=complete job/configure-adb-job --timeout=${timeout_seconds}s
    local wait_exit_code=$?
    
    if [ $wait_exit_code -eq 0 ]; then
        echo "Job completed successfully"
        return 0
    else
        echo "Job failed or timed out"
        
        # Get job status for debugging
        echo "Job status:"
        kubectl -n "${SUB_NAMESPACE}" describe job/configure-adb-job
        
        # Get pod logs for debugging
        echo "Pod logs:"
        kubectl -n "${SUB_NAMESPACE}" logs -l job-name=configure-adb-job --tail=50
        
        return 1
    fi
}

echo "delete configmap configure-adb-status in SUB_NAMESPACE: ${SUB_NAMESPACE}"
kubectl -n "${SUB_NAMESPACE}" delete configmap configure-adb-status --ignore-not-found
kubectl -n "${SUB_NAMESPACE}" apply -f generate-cm.yaml
kubectl -n "${SUB_NAMESPACE}" apply -f configure-adb-status.cm.yaml

update_adb_status(){
    DV_ACTION=$1
    echo "Executing update_adb_status with action: ${DV_ACTION} for SUB_NAMESPACE: ${SUB_NAMESPACE}"

    kubectl -n "${SUB_NAMESPACE}" delete job configure-adb-job --ignore-not-found
	component="configure-adb-status"
	
	# Create SUB_NAMESPACE-specific job file to avoid conflicts
	job_file="configure-adb-job-${SUB_NAMESPACE}.yaml"
	cp configure-adb-job.yaml.tpl "$job_file"
	
	sed -i \
	-e "s|__OCI_REGION__|${oci_region}|g" \
	-e "s|__DV_ACTION__|${DV_ACTION}|g" \
	-e "s|__CURRENT_CLUSTER__|${COMMON_SUB_NAMESPACE^^}|g" \
	-e "s|__DB_SECRET__|${db_secret}|g" \
	-e "s|{{PREFIX}}|${PREFIX}|g" \
	-e "s|{{REGISTRY}}|${REGISTRY}|g" \
	 "$job_file"

	echo "Job configuration for SUB_NAMESPACE ${SUB_NAMESPACE}:"
	cat "$job_file"

	k8s_create_job "${SUB_NAMESPACE}" "${component}" "$job_file" "1500"

    local job_exit_code=$?
    
    # Cleanup SUB_NAMESPACE-specific job file
    rm -f "$job_file"
    
    if [ $job_exit_code -ne 0 ]; then
        echo "configure-adb-job Job failed for SUB_NAMESPACE ${SUB_NAMESPACE} with exit code $job_exit_code"
        exit 1
    fi
    
    echo "Successfully completed ${DV_ACTION} for SUB_NAMESPACE: ${SUB_NAMESPACE}"
}

# Execute based on DV_ACTION
echo "Executing ${DV_ACTION} operation for SUB_NAMESPACE: ${SUB_NAMESPACE}"
update_adb_status "${DV_ACTION}"

echo "Setup completed successfully for SUB_NAMESPACE: ${SUB_NAMESPACE}"
