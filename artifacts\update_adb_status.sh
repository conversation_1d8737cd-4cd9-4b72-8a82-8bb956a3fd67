#!/bin/bash
set -e

source ./common_functions.sh

init(){	
	export CURRENT_CLUSTER=$(echo "$SUB_NAMESPACE" | sed 's/--.*//g')	
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path/env/$MSP_PROJ_NAME/pipeline-$OST_ENVIRONMENT.env ./env/$MSP_PROJ_NAME/ -e k8s
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -f $deployments_artifacts_path/aai_image_versions.props ./ -e k8s
	cp ./env/${MSP_PROJ_NAME}/pipeline-${OST_ENVIRONMENT}.env pipeline.env
	source pipeline.env
	
	# Delete existing configmaps and apply new ones
	kubectl -n "${SUB_NAMESPACE}" delete configmap configure-adb-status --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" apply -f ./oci-utilities/generate-cm.yaml
	kubectl -n "${SUB_NAMESPACE}" apply -f oci-utilities/configure-adb-status.cm.yaml
	
	if [[ -f "aai_image_versions.props" ]];then
		echo "aai_image_versions.props present"
		replace_aai_image_tag_versions "./" "aai_image_versions.props"
	else
		logMessagePatch "INFO" "aai_image_versions.props not present"
	fi
	logMessagePatch "INFO" "updateADBStatus: Env files Loaded..."
}

adb::configure(){
	kubectl -n "${SUB_NAMESPACE}" delete job configure-adb-job --ignore-not-found
	component="configure-adb-job"
	
	# Create SUB_NAMESPACE-specific job file to avoid conflicts
	job_file="configure-adb-job-${SUB_NAMESPACE}.yaml"
	cp oci-utilities/configure-adb-job.yaml.tpl "$job_file"
	
	# Set database secret name
	db_secret="dbaas-details"
	
	sed -i \
	-e "s|__OCI_REGION__|${oci_region}|g" \
	-e "s|__DV_ACTION__|${DV_ACTION}|g" \
	-e "s|__WALLET_PATH__|${BASE_SECRET_URLS}${TENANT_ID}|g" \
	-e "s|__CURRENT_CLUSTER__|${COMMON_SUB_NAMESPACE^^}|g" \
	-e "s|__DB_SECRET__|${db_secret}|g" \
	-e "s|{{PREFIX}}|${PREFIX}|g" \
	-e "s|{{REGISTRY}}|${REGISTRY}|g" \
	-e "s|SUB_NAMESPACE|${SUB_NAMESPACE}|g" \
	"$job_file"

	echo "Job configuration for SUB_NAMESPACE ${SUB_NAMESPACE}:"
	cat "$job_file"

	k8s_create_job "${SUB_NAMESPACE}" "${component}" "$job_file"
	
	local job_exit_code=$?
	
	# Cleanup SUB_NAMESPACE-specific job file
	rm -f "$job_file"
	
	if [ $job_exit_code -ne 0 ]; then
		logMessagePatch "ERROR" "configure-adb-job failed for SUB_NAMESPACE ${SUB_NAMESPACE} with exit code $job_exit_code"
		exit 1
	fi
	
	logMessagePatch "INFO" "Successfully completed ${DV_ACTION} for SUB_NAMESPACE: ${SUB_NAMESPACE}"
}

# Function to execute ADB operation for multiple SUB_NAMESPACEs in parallel
execute_parallel_adb_operations(){
	local action=$1
	export DV_ACTION="$action"
	
	# Validate action parameter
	if [ "$action" != "start" ] && [ "$action" != "stop" ]; then
		logMessagePatch "ERROR" "Invalid action: $action. Expected: start or stop"
		exit 1
	fi
	
	logMessagePatch "INFO" "Starting parallel ADB ${action} operations"
	
	# Read SUB_NAMESPACEs from environment variable
	if [ -n "$SUB_NAMESPACES" ]; then
		# Convert comma-separated string to array
		IFS=',' read -ra SUB_NAMESPACE_ARRAY <<< "$SUB_NAMESPACES"
	else
		logMessagePatch "ERROR" "SUB_NAMESPACES environment variable is not set"
		logMessagePatch "INFO" "Expected format: SUB_NAMESPACES='SUB_NAMESPACE1,SUB_NAMESPACE2,SUB_NAMESPACE3'"
		exit 1
	fi
	
	logMessagePatch "INFO" "SUB_NAMESPACEs to process: ${SUB_NAMESPACE_ARRAY[@]}"
	logMessagePatch "INFO" "Total SUB_NAMESPACEs: ${#SUB_NAMESPACE_ARRAY[@]}"
	
	# Array to store background process PIDs
	pids=()
	failed_SUB_NAMESPACEs=()
	
	# Function to execute ADB operation for a single SUB_NAMESPACE
	execute_single_SUB_NAMESPACE() {
		local ns=$1
		export SUB_NAMESPACE=$ns
		
		logMessagePatch "INFO" "Starting ${action} operation for SUB_NAMESPACE: $ns"
		
		# Initialize and configure for this SUB_NAMESPACE
		init
		adb::configure
		
		local exit_code=$?
		if [ $exit_code -eq 0 ]; then
			logMessagePatch "INFO" "[$ns] Successfully completed ${action}"
		else
			logMessagePatch "ERROR" "[$ns] Failed with exit code $exit_code"
		fi
		
		return $exit_code
	}
	
	# Start parallel execution for each SUB_NAMESPACE
	for ns in "${SUB_NAMESPACE_ARRAY[@]}"; do
		# Trim whitespace
		ns=$(echo "$ns" | xargs)
		
		if [ -n "$ns" ]; then
			logMessagePatch "INFO" "Launching parallel execution for SUB_NAMESPACE: $ns"
			execute_single_SUB_NAMESPACE "$ns" &
			pids+=($!)
		fi
	done
	
	logMessagePatch "INFO" "All parallel executions started. Waiting for completion..."
	
	# Wait for all background processes and collect results
	overall_exit_code=0
	for i in "${!pids[@]}"; do
		pid=${pids[$i]}
		ns=${SUB_NAMESPACE_ARRAY[$i]}
		
		wait $pid
		exit_code=$?
		
		if [ $exit_code -ne 0 ]; then
			failed_SUB_NAMESPACEs+=("$ns")
			overall_exit_code=1
		fi
	done
	
	# Report results
	logMessagePatch "INFO" "=================================================="
	logMessagePatch "INFO" "PARALLEL EXECUTION SUMMARY"
	logMessagePatch "INFO" "=================================================="
	logMessagePatch "INFO" "Total SUB_NAMESPACEs processed: ${#SUB_NAMESPACE_ARRAY[@]}"
	logMessagePatch "INFO" "Successful: $((${#SUB_NAMESPACE_ARRAY[@]} - ${#failed_SUB_NAMESPACEs[@]}))"
	logMessagePatch "INFO" "Failed: ${#failed_SUB_NAMESPACEs[@]}"
	
	if [ ${#failed_SUB_NAMESPACEs[@]} -gt 0 ]; then
		logMessagePatch "ERROR" "Failed SUB_NAMESPACEs: ${failed_SUB_NAMESPACEs[@]}"
	fi
	
	logMessagePatch "INFO" "=================================================="
	
	exit $overall_exit_code
}

# Main execution logic
patch::initialize

# Check if ADB operations are enabled
if [[ -z $ADB_START_STOP_CONFIG || $ADB_START_STOP_CONFIG == "false" ]];then
	logMessagePatch "INFO" "ADB Start/Stop operations are not enabled for this patch. Skipping the stage"
	exit 0
fi

# Check if this is a parallel execution request
if [[ -n "$SUB_NAMESPACES" && -n "$ADB_ACTION" ]]; then
	execute_parallel_adb_operations "$ADB_ACTION"
else
	# Single SUB_NAMESPACE execution (backward compatibility)
	if [[ -z "$DV_ACTION" ]]; then
		logMessagePatch "ERROR" "DV_ACTION is not set. Please set DV_ACTION to 'start' or 'stop'"
		exit 1
	fi
	
	init
	adb::configure
fi
