#!/bin/bash
init(){	
	
	export CURRENT_CLUSTER=$(echo "$SUB_NAMESPACE" | sed 's/--.*//g')	
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -rf $deployments_artifacts_path/env/$MSP_PROJ_NAME/pipeline-$OST_ENVIRONMENT.env ./env/$MSP_PROJ_NAME/ -e k8s
	./artstore -p ${MSP_PROJ_NAME} -g ${REGION_CODE} -n ${TENANCY}  file pull -f $deployments_artifacts_path/aai_image_versions.props ./ -e k8s
	cp ./env/${MSP_PROJ_NAME}/pipeline-${OST_ENVIRONMENT}.env pipeline.env
	source pipeline.env
	kubectl -n "${SUB_NAMESPACE}" delete configmap configure-dv-status --ignore-not-found
	kubectl -n "${SUB_NAMESPACE}" apply -f ./oci-utilities/generate-cm.yaml
	kubectl -n "${SUB_NAMESPACE}" apply -f oci-utilities/configure-dv-status.cm.yaml
	
	if [[ -f "aai_image_versions.props" ]];then
		echo "aai_image_versions.props present"
		replace_aai_image_tag_versions "./" "aai_image_versions.props"
	else
		logMessagePatch "INFO" "aai_image_versions.props not present"
	fi
	logMessagePatch "INFO" "updateDVStatus: Env files Loaded..."
	
}

dv::configure(){
	kubectl -n "${SUB_NAMESPACE}" delete job configure-dv-restart --ignore-not-found
	component="configure-dv-restart"
	
	cp oci-utilities/configure-dv-restart.job.yaml.tpl oci-utilities/configure-dv-restart.job.yaml
	#cat oci-utilities/configure-dv-restart.job.yaml
	sed -i \
	-e "s|__OCI_REGION__|${oci_region}|g" \
	-e "s|__DV_ACTION__|${DV_ACTION}|g" \
	-e "s|__WALLET_PATH__|${BASE_SECRET_URLS}${TENANT_ID}|g" \
	-e "s|__CURRENT_CLUSTER__|${COMMON_NAMESPACE^^}|g" \
	./oci-utilities/configure-dv-restart.job.yaml

	k8s_create_job "${SUB_NAMESPACE}" "${component}" oci-utilities/configure-dv-restart.job.yaml
}

patch::initialize
if [[ -z $ON_OFF_DV_CONFIG || $ON_OFF_DV_CONFIG == "false" ]];then
	logMessagePatch "INFO" "DV Status update is not enabled for this patch. Skipping the stage"
	exit 0
fi

init
dv::configure
