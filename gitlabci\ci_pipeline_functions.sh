#!/bin/bash
source ./artifacts/common_functions.sh
mkdir -p artifacts/env/${MSP_PROJ_NAME}
PATCH_HOME="$(pwd)"
IMAGE_VERSION=${IMAGE_VERSION:-$PATCH_VERSION}
logMessage(){
	Datelog=$(date +%Y-%m-%d)
    timestamplog=$(date +%H:%M:%S)
    Targethost=$HOSTNAME
    severity="$1"
    LoggerName="DFCS_PATCH_UPGRADE"
    messageId="$3"
	messagebody="$2"
	if [[ ${severity^^} == "INFO" ]];then
		echo -e "\033[1;32m$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody\033[0m"
	elif [[ ${severity^^} == "WARN" || ${severity^^} == "WARNING" ]];then
		echo -e "\033[1;38;5;214m$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody\033[0m"
	elif [[ ${severity^^} == "ERROR" || ${severity^^} == "FATAL"  ]];then
		echo -e "\033[1;31m$Datelog $timestamplog -- $severity -- $messageId -- $messagebody\033[0m"
	else
		echo -e "$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody"
	fi
}


versionCheckCISide(){
pattern="^[0-9]+(\.[0-9]+){2}$"
if [[ ! ($1 =~ $pattern && $3 =~  $pattern) ]]; then
	echo "true"
	return
fi
res=$(comp $1 $3)
if [[ $res -eq 0 ]];then
	op="eq"
elif [[ $res -eq 1 ]];then
	op='gt'
elif [[ $res -eq 2 ]];then
	op='lt'
fi
if [[ ( "$2" == "ge") && ( $op == "eq" || $op == "gt" ) ]];then
	echo true
elif [[ ("$2" == "le") && ($op == "eq" || $op == "lt") ]];then
	echo true
elif [[ ("$2" == "gt") && ($op == "gt") ]];then
	echo true
elif [[ ("$2" == "lt") && ($op == "lt") ]];then
	echo true
elif [[ ("$2" == "eq") && ("$op" == "eq") ]];then
	echo true
elif [[ ("$2" == "ne") && ("$op" == "gt" || "$op" == "lt") ]];then
	echo true
else
	echo false
fi		
}


initialize(){
		
	deployments=dfcs-deployment-artifacts/$PATCH_VERSION
	logMessage "INFO" "Deployments Artifacts path: $deployments"
	
	envfile="$deployments/env/$MSP_PROJ_NAME/pipeline-${OST_ENVIRONMENT}.env"
	logMessage "INFO" "envfile path: $envfile"
	
	artstore -p ${MSP_PROJ_NAME} file pull -f $envfile ./artifacts/env/${MSP_PROJ_NAME}/
	fileName=`basename "$envfile"`
	REGION_CODE=$(cat ./artifacts/env/${MSP_PROJ_NAME}/$fileName | grep -w REGION_CODE)
	REGION_CODE=$(echo ${REGION_CODE} | cut -d '=' -f2 | sed "s/\"//g")
	
	if [[ (${PATCH_STAGE^^} != "PUBLISH" || ${PATCH_STAGE^^} != "SCAN" || ${PATCH_STAGE^^} != "SYNC") ]];then
	
		artstore -p ${MSP_PROJ_NAME} file pull -rf $deployments/payload.json ./	
		chmod 755 ./artifacts/parse_json_payload.py
		./artifacts/parse_json_payload.py "payload.json" "$PATCH_HOME" "$OST_ENVIRONMENT" "$OST_CLASS" "$TENANT_ID"
		sed -i \
			-e "s|__PATCH_ID__|${PATCH_ID}|g" \
		   	$PATCH_HOME/patch_pipeline_services.env				
		source $PATCH_HOME/patch_pipeline_services.env	
	
	fi
	logMessage "INFO" "All CI side Env Loaded..."

}

set_ci_params() {	
	
	MSP_PROJ_NAME="${1}"
	sed	\
		-e "s|__MSP_PROJ_NAME__|${1}|g" \
		-e "s|__ARTPROJECT__|${1}|g" \
		-e "s|__PATCH_VERSION__|${PATCH_VERSION}|g" \
		-e "s|__OST_ENVIRONMENT__|${OST_ENVIRONMENT}|g" \
		-e "s|__OST_CLASS__|${OST_CLASS}|g" \
		-e "s|__MSP_SERVICE__|${MSP_SERVICE}|g" \
		-e "s|__PATCH_STAGE__|${PATCH_STAGE}|g" \
		-e "s|__PATCH_ID__|${PATCH_ID}|g" \
		-e "s|__PATCH_ARTSTORE_ARTIFACT_PATH__|${PATCH_ARTSTORE_ARTIFACT_PATH}|g" \
		-e "s|__TENANT_ID__|${TENANT_ID}|g" \
		-e "s|__TENANT_STRING__|${TENANT_STRING}|g" \
		-e "s|__PATCH_BRANCH__|${PATCH_BRANCH}|g" \
		-e "s|__PATCH_SCRIPT__|${2}|g" \
		-e "s|__IS_ADW__|${IS_ADW}|g" \
		-e "s|__TENANCY__|${TENANCY}|g" \
		-e "s|__ONBOARD_DIS_TENANT__|${ONBOARD_DIS_TENANT}|g" \
		-e "s|__CI_DIS_ARTIFACT_VERSION__|${CI_DIS_ARTIFACT_VERSION}|g" \
		-e "s|__DR__|${DR}|g" \
		-e "s|__DIS_FORCE_DELETE__|${DIS_FORCE_DELETE}|g" \
		-e "s|__IS_MAINTENANCE_ENABLED__|${IS_MAINTENANCE_ENABLED}|g" \
		-e "s|__DV_ACTION__|${DV_ACTION}|g" \
		-e "s|__CI_PIPELINE_ID__|${CI_PIPELINE_ID}|g" \
		-e "s|__API_POLL_COUNT__|${API_POLL_COUNT}|g" \
		-e "s|__API_POLL_INTERVAL__|${API_POLL_INTERVAL}|g" \
		-e "s|__REGION_CODE__|${REGION_CODE}|g" \
		-e "s|__PATCH_ACTION__|${PATCH_ACTION}|g" \
		wos/deploy.yaml > ${deployer_config_yaml}	
	
		cat ${deployer_config_yaml}
		
}

tool::deploy() {
	
	if [ "${GITLAB_CI}" = "true" ]; then
	  /usr/OST/app/deployer changeJob \
		  --summary "${JIRA_SUMMARY_DESC_STRING}" \
		  --description "${JIRA_SUMMARY_DESC_STRING}" \
		  ${deployer_config_yaml}
	else
	  artstore -p shared image pull -f /${CI_DEPLOYER_IMAGE_PATH}
	  docker run -t --rm -v ${deployer_config_yaml}:/usr/OST/app/etc/config.yaml:ro \
		  -e WF_JWT \
		  ${CI_DEPLOYER_IMAGE_ABS_PATH} /usr/OST/app/deployer changeJob \
		  --summary "${JIRA_SUMMARY_DESC_STRING}" \
		  --description "${JIRA_SUMMARY_DESC_STRING}" \
		  /usr/OST/app/etc/config.yaml
	fi
}

patch::deploy_services(){
	
	export WF_JWT="$1"
	TMPDIR=$(mktemp -d) || exit 1
	trap "rm -rf $TMPDIR" EXIT
	export deployer_config_yaml=${TMPDIR}/deploy.yaml
	set_ci_params "$2" "$3"
	export JIRA_SUMMARY_DESC_STRING="$2 ${CI_PROJECT_NAME} WOS Deploy"
	tool::deploy
	if [ $? -ne 0 ]; then
		exit -1
	fi 	

}

patch::precheck(){
    initialize
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "precheck.sh" 
	logMessage "INFO" "precheck stage complete."
}

patch::enable_maintenance(){
	
	IS_MAINTENANCE_ENABLED="true"
	initialize
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "maintenance.sh"
	logMessage "INFO" "enable maintenance stage complete."
	
}

patch::disable_dv(){
	DV_ACTION="disable"
	initialize
	if [[ -z $ON_OFF_DV_CONFIG || $ON_OFF_DV_CONFIG == "false" ]];then
		logMessage "INFO" "DV Status update has not been enabled for this patch. Skipping the stage"
		exit 0
	fi
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "update_dv_status.sh" 
	logMessage "INFO" "Disable DV stage complete."
}

patch::pushs3wallet(){

	    initialize
		patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "push_wallet_to_s3.sh" 
		logMessage "INFO" "FSAFND push s3wallet complete."
		patch::deploy_services "${DFCS_DAT}" "fsgbu-dfcs" "push_wallet_to_s3.sh" 
		logMessage "INFO" "dfcs Push s3wallet stage complete."
	
}

patch::deploy_db_scripts(){

	logMessage "INFO" "deploy-db-scripts..."
	initialize
		if [[ ("$EXECUTE_SCRIPTS" == "true" || "$SKIP_SC" == "false") ]];then
			patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "apply_db_scripts.sh" 
			patch::deploy_services "${DFCS_DAT}" "fsgbu-dfcs" "apply_db_scripts.sh"
		else
			logMessage "INFO" "SchemaCreator execution and Script execution has not been enabled for this patch. Skipping the stage"
			exit 0
		fi

}

patch::wtss_registry(){
	logMessage "INFO" "deploy-wtss, wtss_registry.sh"
	initialize
		if [[ "$UPDATE_WTSS" == "true" ]];then
			patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "wtss_registry.sh"
			patch::deploy_services "${DFCS_DAT}" "fsgbu-dfcs" "wtss_registry.sh"
		else
			logMessage "INFO" "WTSS deployment has not been enabled for this patch. Skipping the stage"
			exit 0
		fi
}

patch::deploy_aai_services(){

	#deploy in fsafnd
	initialize
	if [[ $(versionCheckCISide $PATCH_VERSION ge "25.3.1") == "true" ]];then
		PATCH_ACTION="PRE_UPGRADE"
		logMessage "LOG" "Pre Upgrade deployment submitted to fsgbu-fsafnd namespace."
		patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "deploy_aai_services.sh"
		logMessage "LOG" "Pre Upgrade deployment submitted to fsgbu-dfcs namespace."
		patch::deploy_services "${DFCS_DAT}" "fsgbu-dfcs" "deploy_aai_services.sh"
	fi
		PATCH_ACTION="FRC_UPGRADE"
		logMessage "LOG" "FRC UPGRADE deployment submitted to fsgbu-fsafnd namespace."
		patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "deploy_aai_services.sh"
	if [[ $(versionCheckCISide $PATCH_VERSION ge "25.3.1") == "true" ]];then
		PATCH_ACTION="POST_UPGRADE"
		logMessage "LOG" "Post Upgrade deployment submitted to fsgbu-fsafnd namespace."
		patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "deploy_aai_services.sh"
		logMessage "LOG" "Post Upgrade deployment submitted to fsgbu-dfcs namespace."
		patch::deploy_services "${DFCS_DAT}" "fsgbu-dfcs" "deploy_aai_services.sh"
	fi

		
}


patch::deploy_fss_services(){
	
	initialize
	if [[ -z "$DEPLOY_FSS_SERVICES" || "$DEPLOY_FSS_SERVICES" == "false" ]];then
		logMessage "INFO" "Common FSS Services deployment has not been enabled for this patch. Skipping the stage"
		exit 0
	fi
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "deploy_fss_services.sh" 
	logMessage "INFO" "Deploy sfs stage complete."
}

patch::deploy_app_services(){
	logMessage "INFO" "deploy-app-services..."
	initialize
		if [[ -z "$FSAFND_APP_DEPLOYMENT_NAMES" && -z "$DFCS_APP_DEPLOYMENT_NAMES" ]];then	
			logMessage "INFO" "App services deployment has not been enabled for this patch, Skipping the stage"
			exit 0
		elif [[ (! -z "$DFCS_APP_DEPLOYMENT_NAMES") && (! -z "$FSAFND_APP_DEPLOYMENT_NAMES") ]];then
			patch::deploy_services "$FSAFND_DAT" "fsgbu-fsafnd" "deploy_app_services.sh" 
			logMessage "INFO" "App services completed with fsgbu-fsafnd side deployments"
			patch::deploy_services "${DFCS_DAT}" "fsgbu-dfcs" "deploy_app_services.sh"
			logMessage "INFO" "App services completed with fsgbu-dfcs side deployments"
		elif [[ -z "$DFCS_APP_DEPLOYMENT_NAMES" && (! -z "$FSAFND_APP_DEPLOYMENT_NAMES") ]];then
			patch::deploy_services "$FSAFND_DAT" "fsgbu-fsafnd" "deploy_app_services.sh" 
			logMessage "INFO" "App services deployment has not been enabled for fsgbu-dfcs deployments, Skipping for dfcs"
		elif [[ -z "$FSAFND_APP_DEPLOYMENT_NAMES" && (! -z "$DFCS_APP_DEPLOYMENT_NAMES") ]];then
			patch::deploy_services "${DFCS_DAT}" "fsgbu-dfcs" "deploy_app_services.sh"
			logMessage "INFO" "App services deployment has not been enabled for fsgbu-fsafnd deployments, Skipping for fsafnd"
		fi
}

patch::healthCheck(){
    
	initialize
	MSPPROJNAME="$1"
	PATCH_ACTION="$2"
	logMessage "INFO" "healthCheck for $PATCH_ACTION"
	TOKEN_NM=$([[ ${MSPPROJNAME} == "fsgbu-fsafnd" ]] && echo "${FSAFND_DAT}" || echo "${DFCS_DAT}")
	patch::deploy_services "${TOKEN_NM}" $MSPPROJNAME "health_check.sh"
	
}

patch::upgrade_dis(){
	
	logMessage "INFO" "deploy-dis-artifacts....called"
	initialize
		if [[ $UPGRADE_DIS != "" && $UPGRADE_DIS == "false" ]];then
			logMessage "INFO" "DIS Upgrade has not been enabled for this patch. Skipping the stage"
			exit 0
		fi
		patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "ugrade_dis.sh" 
	    logMessage "INFO" "ugrade dis stage complete."
}

patch::dfcsContent(){
    
	initialize
	logMessage "INFO" "dfcsContent job execution starts..."
	if [[ $DFCS_CONTENT != "" && $DFCS_CONTENT == "false" ]];then
			logMessage "INFO" "DFCS Content Job Execution has not been enabled for this patch. Skipping the stage"
			exit 0
		fi
		patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "dfcs_content_job.sh" 
	    logMessage "INFO" "DFCS Content stage complete."
	
}

patch::dfcsDeployment(){
    
	initialize
	logMessage "INFO" "DFCS Deployment job execution starts..."
	if [[ $INITIATE_DEPLOYMENT != "" && $INITIATE_DEPLOYMENT == "false" ]];then
			logMessage "INFO" "DFCS Content Job Execution has not been enabled for this patch. Skipping the stage"
			exit 0
		fi
		patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "dfcs_deployment_job.sh" 
	    logMessage "INFO" "Deployment Completed"
	
}

patch::enable_dv(){
	
	DV_ACTION="enable"
	initialize
	if [[ -z $ON_OFF_DV_CONFIG || $ON_OFF_DV_CONFIG == "false" ]];then
		logMessage "INFO" "DV Status update has not been enabled for this patch. Skipping the stage"
		exit 0
	fi
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "update_dv_status.sh" 
	logMessage "INFO" "Enable DV stage complete."

}

patch::disable_maintenance(){

	IS_MAINTENANCE_ENABLED="false"
	initialize
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "maintenance.sh"
	logMessage "INFO" "disable maintenance stage complete."
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "set-upgrade-flag.sh"
	logMessage "INFO" "Enable upgrade flag complete"
}

patch::adb_start(){

	ADB_ACTION="start"
	initialize
	if [[ -z $ADB_START_STOP_CONFIG || $ADB_START_STOP_CONFIG == "false" ]];then
		logMessage "INFO" "ADB Start/Stop operations have not been enabled for this patch. Skipping the stage"
		exit 0
	fi
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "update_adb_status.sh"
	logMessage "INFO" "ADB Start stage complete."

}

patch::adb_stop(){

	ADB_ACTION="stop"
	initialize
	if [[ -z $ADB_START_STOP_CONFIG || $ADB_START_STOP_CONFIG == "false" ]];then
		logMessage "INFO" "ADB Start/Stop operations have not been enabled for this patch. Skipping the stage"
		exit 0
	fi
	patch::deploy_services "${FSAFND_DAT}" "fsgbu-fsafnd" "update_adb_status.sh"
	logMessage "INFO" "ADB Stop stage complete."

}

export WF_JWT=${FSAFND_DAT}
TENANT_ID=${TENANT_ID:-NA}
OST_ENVIRONMENT=${OST_ENVIRONMENT:-cndevcorp7-phx}
MSP_PROJ_NAME=${MSP_PROJ_NAME:-fsgbu-fsafnd}
NAMESPACE=${NAMESPACE:-${MSP_PROJ_NAME}}
MSP_SERVICE=${MSP_PROJ_NAME^^}
OST_CLASS=${OST_CLASS:-dev}
PATCH_VERSION=${PATCH_VERSION:-dev}
IS_ADW=${IS_ADW:-false}
PATCH_BRANCH=${CI_COMMIT_REF_NAME}
PATCH_ID=${PATCH_ID:-base-patch}
ONBOARD_DIS_TENANT=${ONBOARD_DIS_TENANT:-false}
CI_DIS_ARTIFACT_VERSION=${DIS_ARTIFACT_VERSION:-NA}
DIS_FORCE_DELETE=${DIS_FORCE_DELETE:=false}
DR=${DR:-false}
IS_MAINTENANCE_ENABLED="true"
PATCH_VERSION=${PATCH_VERSION:-dev}
API_POLL_INTERVAL=${API_POLL_INTERVAL:-10}
API_POLL_COUNT=${API_POLL_COUNT:-5}

export TENANT_STRING=$(echo ${TENANT_ID} | cut -d '-' -f1 | sed "s/\"//g")

logMessage "INFO" "BUILD_BRANCH: $CI_COMMIT_REF_NAME"

if [[ "$OST_CLASS" == "dev" ]];then
	export TENANCY="devcorp"
	export WF_JWT=${FSAFND_DAT}
elif [[ "$OST_CLASS" == "prod" ]];then
	export TENANCY="prod"
	export WF_JWT=${FSAFND_MAT}
	export FSAFND_DAT=$FSAFND_MAT
	export DFCS_DAT=$DFCS_MAT
fi

if [[ -z "$FSAFND_DAT" ||  -z "$DFCS_DAT" ]];then
	logMessage "ERROR" "FSAFND_DAT/MAT and DFCS_DAT/MAT is not Configured/Passed for the project"
	exit -1
fi

sed -i \
	-e "s|__PATCH_VERSION__|${PATCH_VERSION}|g" \
	-e "s|__MSP_SERVICE__|${MSP_SERVICE}|g" \
	-e "s|__OST_CLASS__|${OST_CLASS}|g" \
	-e "s|__TENANT_ID__|${TENANT_ID}|g" \
	-e "s|__MSP_PROJ_NAME__|${MSP_PROJ_NAME}|g" \
	-e "s|__OST_ENVIRONMENT__|${OST_ENVIRONMENT}|g" \
	-e "s|__PATCH_ID__|${PATCH_ID}|g" \
	-e "s|__IMAGE_VERSION__|${IMAGE_VERSION}|g" \
	-e "s|__IS_ADW__|${IS_ADW}|g" \
	ci_common.env

source ci_common.env
echo "**********************************************"
logMessage "INFO" "CLUSTER: $OST_ENVIRONMENT"
logMessage "INFO" "TENANT_ID=$TENANT_ID"
logMessage "INFO" "PATCH_VERSION: $PATCH_VERSION"
VERSION=${PATCH_VERSION}
echo "**********************************************"




