#!/bin/bash
source ./artifacts/common_functions.sh
mkdir -p artifacts/env/${MSP_PROJ_NAME}
PATCH_HOME="$(pwd)"

logMessage(){
	Datelog=$(date +%Y-%m-%d)
    timestamplog=$(date +%H:%M:%S)
    Targethost=$HOSTNAME
    severity="$1"
    LoggerName="ADB_PIPELINE"
    messageId="$3"
	messagebody="$2"
	if [[ ${severity^^} == "INFO" ]];then
		echo -e "\033[1;32m$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody\033[0m"
	elif [[ ${severity^^} == "WARN" || ${severity^^} == "WARNING" ]];then
		echo -e "\033[1;38;5;214m$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody\033[0m"
	elif [[ ${severity^^} == "ERROR" || ${severity^^} == "FATAL"  ]];then
		echo -e "\033[1;31m$Datelog $timestamplog -- $severity -- $messageId -- $messagebody\033[0m"
	else
		echo -e "$Datelog $timestamplog  $LoggerName -- $severity -- $messageId -- $messagebody"
	fi
}

initialize(){
	logMessage "INFO" "Initializing ADB pipeline environment"

	# Set default values for ADB operations
	export ADB_ACTION=${ADB_ACTION:-"start"}
	export SUB_NAMESPACES=${SUB_NAMESPACES:-""}
	export ADB_START_STOP_CONFIG=${ADB_START_STOP_CONFIG:-"true"}

	logMessage "INFO" "ADB_ACTION: $ADB_ACTION"
	logMessage "INFO" "SUB_NAMESPACES: $SUB_NAMESPACES"
	logMessage "INFO" "ADB_START_STOP_CONFIG: $ADB_START_STOP_CONFIG"

	logMessage "INFO" "Environment variables loaded successfully"
}

patch::deploy_services(){
	local script_name="$3"
	logMessage "INFO" "Executing ADB operation script: $script_name"

	# Execute the script directly
	if [ -f "artifacts/$script_name" ]; then
		chmod +x "artifacts/$script_name"
		cd artifacts
		./$script_name
		local exit_code=$?
		cd ..
		if [ $exit_code -ne 0 ]; then
			logMessage "ERROR" "Script execution failed: $script_name"
			exit -1
		fi
	else
		logMessage "ERROR" "Script not found: artifacts/$script_name"
		exit -1
	fi
}

patch::adb_start(){
	export ADB_ACTION="start"
	initialize
	if [[ -z $ADB_START_STOP_CONFIG || $ADB_START_STOP_CONFIG == "false" ]];then
		logMessage "INFO" "ADB Start/Stop operations have not been enabled for this patch. Skipping the stage"
		exit 0
	fi
	patch::deploy_services "" "" "update_adb_status.sh"
	logMessage "INFO" "ADB Start stage complete."
}

patch::adb_stop(){
	export ADB_ACTION="stop"
	initialize
	if [[ -z $ADB_START_STOP_CONFIG || $ADB_START_STOP_CONFIG == "false" ]];then
		logMessage "INFO" "ADB Start/Stop operations have not been enabled for this patch. Skipping the stage"
		exit 0
	fi
	patch::deploy_services "" "" "update_adb_status.sh"
	logMessage "INFO" "ADB Stop stage complete."
}

# Environment variables
TENANT_ID=${TENANT_ID:-NA}
OST_ENVIRONMENT=${OST_ENVIRONMENT:-cndevcorp7-phx}
MSP_PROJ_NAME=${MSP_PROJ_NAME:-fsgbu-fsafnd}
NAMESPACE=${NAMESPACE:-${MSP_PROJ_NAME}}






