#!/bin/bash

chmod +x ./gitlabci/ci_pipeline_functions.sh
source ./gitlabci/ci_pipeline_functions.sh

if [[ -z "$TENANT_ID" ]];then
	logMessage "ERROR" "TENANT_ID is Mandatory."
	exit -1
fi

case "${PATCH_STAGE}" in
   "adb_start") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::adb_start
   ;;
   "adb_stop") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::adb_stop
   ;;
   *) logMessage "ERROR" "Unknown PATCH_STAGE: $PATCH_STAGE"
	exit -1
   ;;
esac

