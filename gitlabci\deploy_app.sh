#!/bin/bash

chmod +x ./gitlabci/ci_pipeline_functions.sh
source ./gitlabci/ci_pipeline_functions.sh

if [[ -z "$TENANT_ID" ]];then
	logMessage "ERROR" "TENANT_ID is Mandatry."
	exit -1
fi	

case "${PATCH_STAGE}" in 
   "precheck") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::precheck
   ;;
   "start_maintenance") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::enable_maintenance
   ;;
   "disableDataVault") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::disable_dv
   ;;
   "pushs3wallet") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::pushs3wallet 
   ;;
   "dbScripts") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::deploy_db_scripts  
   ;; 
   "wtss_registry") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::wtss_registry 
   ;;
   "aaiServices") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::deploy_aai_services 
   ;;
   "fssServices") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::deploy_fss_services
   ;;
   "appServices") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::deploy_app_services  
   ;;
   "catalog_hc") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::healthCheck "fsgbu-fsafnd" "catalog"
   ;;
   "dsui_hc") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::healthCheck "fsgbu-fsafnd" "dsa-ui"
   ;;
   "dsEngine_hc") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::healthCheck "fsgbu-fsafnd" "dsa-core-engine"
   ;;
   "dfcscore_hc") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::healthCheck "fsgbu-dfcs" "dfcscore"
   ;;
   "disArtifacts") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::upgrade_dis 
   ;;
   "contentJob") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::dfcsContent 
   ;;
   "initiateDeployment") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::dfcsDeployment 
   ;;
   "disArtifacts") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::dfcsDeployment 
   ;;
   "enableDataVault") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::enable_dv
   ;;
   "release_environment") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::disable_maintenance
   ;;
   "adb_start") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::adb_start
   ;;
   "adb_stop") logMessage "INFO" "PATCH_STAGE is $PATCH_STAGE"
	patch::adb_stop
   ;;
esac

